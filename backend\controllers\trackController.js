const Visit = require('../models/Visit');

// POST /api/track/visit
exports.logVisit = async (req, res) => {
  const { section } = req.body;
  const ip = req.visitorIP || req.ip;
  if (!section) return res.status(400).json({ message: 'Section is required' });
  try {
    await Visit.create({ ip, section });
    res.status(201).json({ message: 'Visit logged' });
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
}; 