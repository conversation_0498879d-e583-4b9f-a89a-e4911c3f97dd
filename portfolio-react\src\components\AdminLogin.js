import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const AdminLogin = () => {
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [loginMessage, setLoginMessage] = useState('');
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginMessage('');
    console.log('Login handler called');
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: loginEmail, password: loginPassword })
      });
      const data = await response.json();
      if (data.token) {
        localStorage.setItem('token', data.token);
        setLoginMessage('Login successful! Redirecting...');
        setTimeout(() => navigate('/admin/dashboard'), 1000);
      } else {
        setLoginMessage(data.message || 'Login failed');
      }
    } catch (err) {
      setLoginMessage('Network error');
    }
  };

  return (
    <div style={{ margin: '2rem 0', padding: '1rem', border: '1px solid #ccc', maxWidth: 400 }}>
      <h3>Admin Login</h3>
      <form onSubmit={handleLogin} autoComplete="off">
        <input
          name="email"
          type="email"
          placeholder="Email"
          value={loginEmail}
          onChange={e => setLoginEmail(e.target.value)}
          required
          style={{ display: 'block', marginBottom: 8, width: '100%' }}
        />
        <input
          name="password"
          type="password"
          placeholder="Password"
          value={loginPassword}
          onChange={e => setLoginPassword(e.target.value)}
          required
          style={{ display: 'block', marginBottom: 8, width: '100%' }}
        />
        <button type="submit" style={{ width: '100%' }}>Login</button>
      </form>
      {loginMessage && <div style={{ marginTop: 8, color: loginMessage.includes('success') ? 'green' : 'red' }}>{loginMessage}</div>}
    </div>
  );
};

export default AdminLogin; 