const Admin = require('../models/Admin');
const Visit = require('../models/Visit');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');

// POST /api/admin/login
exports.login = async (req, res) => {
  // Sanitize input
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: 'Invalid input' });
  }
  const { email, password } = req.body;
  try {
    // Find admin in database
    const admin = await Admin.findOne({ email });
    if (!admin) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: admin not found`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Compare password with hashed password from database
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: wrong password`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const token = jwt.sign({ email }, process.env.JWT_SECRET, { expiresIn: '1h' });
    console.log(`[LOGIN SUCCESS] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Email: ${email}`);
    res.json({ token });
  } catch (err) {
    console.log('LOGIN ERROR:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/admin/dashboard
exports.dashboard = async (req, res) => {
  try {
    const totalVisits = await Visit.countDocuments();
    const visits = await Visit.find().sort({ timestamp: -1 });
    // Aggregate views per section
    const sectionStats = await Visit.aggregate([
      { $group: { _id: '$section', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    // Calculate percentages
    const statsWithPercent = sectionStats.map(s => ({
      section: s._id,
      count: s.count,
      percent: totalVisits ? ((s.count / totalVisits) * 100).toFixed(1) : 0
    }));
    res.json({
      totalVisits,
      visits: visits.map(v => ({ ip: v.ip, timestamp: v.timestamp, section: v.section })),
      sectionStats: statsWithPercent
    });
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
}; 