import React, { useEffect, useState } from 'react';
import './AdminDashboard.css';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaChartPie, FaListUl, FaSignOutAlt, FaCrown, FaShieldAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchStats = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        return;
      }
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/dashboard`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error('Dashboard fetch error:', error);
        setError('Error fetching stats: ' + error.message);
      }
    };
    fetchStats();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/');
  };

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-error">{error}</div>
        <button onClick={handleLogout} className="dashboard-logout-btn">
          <FaSignOutAlt /> Logout
        </button>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-loading">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <button onClick={handleLogout} className="dashboard-logout-btn">
        <FaSignOutAlt /> Logout
      </button>

      <h1 className="dashboard-title">
        <FaCrown /> Admin Dashboard <FaShieldAlt />
      </h1>

      <div className="dashboard-stats-grid">
        {/* Total Visits Card */}
        <div className="dashboard-stat-card">
          <FaUsers className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Total Visits</div>
          <div className="dashboard-stat-value">{stats.totalVisits || 0}</div>
        </div>

        {/* Section Statistics Card */}
        <div className="dashboard-stat-card dashboard-section-stats">
          <FaChartPie className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Section Views</div>
          <div className="dashboard-section-stats-grid">
            {stats.sectionStats && stats.sectionStats.length > 0 ? (
              stats.sectionStats.map((section, index) => (
                <div key={index} className="dashboard-section-stat">
                  <span className="section-name">{section.section}</span>
                  <span className="section-count">{section.count}</span>
                  <span className="section-percent">({section.percent}%)</span>
                </div>
              ))
            ) : (
              <div className="dashboard-empty">No section data available</div>
            )}
          </div>
        </div>

        {/* Recent Visitors Card */}
        <div className="dashboard-stat-card dashboard-visitor-log">
          <FaListUl className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Recent Visitors</div>
          <div className="dashboard-visitor-log-list">
            {stats.visits && stats.visits.length > 0 ? (
              <ul>
                {stats.visits.slice(0, 10).map((visit, index) => (
                  <li key={index}>
                    <span className="visitor-timestamp">
                      {new Date(visit.timestamp).toLocaleString()}
                    </span>
                    <span className="visitor-ip">{visit.ip}</span>
                    <span className="visitor-section">{visit.section}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="dashboard-empty">No visitor data available</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;